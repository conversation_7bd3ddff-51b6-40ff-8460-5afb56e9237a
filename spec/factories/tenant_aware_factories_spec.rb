# frozen_string_literal: true

# ABOUTME: Tests for tenant-aware factory enhancements that prevent TYM-30 context mismatch scenarios
# ABOUTME: by validating factory-created objects align with ActsAsTenant current_tenant context.

require 'rails_helper'

RSpec.describe 'Tenant-aware factory enhancements', type: :model do
  include TenantAwareFactories

  let(:company_a) { create(:company, name: 'Company A') }
  let(:company_b) { create(:company, name: 'Company B') }
  let(:user) { create(:user) }
  let(:role) { create(:role, name: 'employee') }

  before do
    create(:company_user_role, user: user, company: company_a, role: role)
    create(:company_user_role, user: user, company: company_b, role: role)
  end

  describe 'Contract factory enhancements' do
    describe ':tenant_aware trait' do
      it 'logs warning when context mismatch is detected' do
        expect(Rails.logger).to receive(:warn)
          .with(/CONTRACT_FACTORY.*Potential context mismatch detected/)
        expect(Rails.logger).to receive(:warn)
          .with(/This could cause TYM-30 debugging hell scenario/)

        ActsAsTenant.with_tenant(company_a) do
          create(:contract, :tenant_aware, company: company_b)
        end
      end

      it 'does not log warning when contexts are aligned' do
        expect(Rails.logger).not_to receive(:warn)

        ActsAsTenant.with_tenant(company_a) do
          create(:contract, :tenant_aware, company: company_a)
        end
      end
    end

    describe ':in_current_tenant trait' do
      it 'creates contract in current tenant context' do
        ActsAsTenant.with_tenant(company_a) do
          contract = create(:contract, :in_current_tenant)
          
          expect(contract.company).to eq(company_a)
        end
      end

      it 'creates new company if no current tenant' do
        ActsAsTenant.current_tenant = nil
        
        expect(Rails.logger).to receive(:warn)
          .with(/No current tenant set, created new company for contract/)
        
        contract = create(:contract, :in_current_tenant)
        expect(contract.company).to be_present
        expect(contract.company).to be_persisted
      end
    end

    describe ':with_validated_context trait' do
      it 'raises error when contexts are misaligned (TYM-30 prevention)' do
        ActsAsTenant.with_tenant(company_a) do
          expect { create(:contract, :with_validated_context, company: company_b) }
            .to raise_error(StandardError)
            .with_message(/CONTRACT FACTORY CONTEXT MISMATCH/)
        end
      end

      it 'includes detailed TYM-30 explanation in error message' do
        ActsAsTenant.with_tenant(company_a) do
          expect { create(:contract, :with_validated_context, company: company_b) }
            .to raise_error(StandardError) do |error|
              expect(error.message).to include("This is the exact TYM-30 scenario")
              expect(error.message).to include("SOLUTION:")
              expect(error.message).to include("ActsAsTenant.with_tenant")
            end
        end
      end

      it 'passes validation when contexts are aligned' do
        ActsAsTenant.with_tenant(company_a) do
          expect { create(:contract, :with_validated_context, company: company_a) }
            .not_to raise_error
        end
      end
    end
  end

  describe 'Event factory enhancements' do
    describe ':tenant_aware trait' do
      it 'logs warning for event company context mismatch' do
        expect(Rails.logger).to receive(:warn)
          .with(/EVENT_FACTORY.*Event context mismatch detected/)

        ActsAsTenant.with_tenant(company_a) do
          create(:event, :tenant_aware, company: company_b)
        end
      end

      it 'logs warning for contract context mismatch' do
        contract_b = nil
        ActsAsTenant.with_tenant(company_b) do
          contract_b = create(:contract, company: company_b)
        end

        expect(Rails.logger).to receive(:warn)
          .with(/EVENT_FACTORY.*Event contract context mismatch/)

        ActsAsTenant.with_tenant(company_a) do
          create(:event, :tenant_aware, contract: contract_b)
        end
      end
    end

    describe ':in_current_tenant trait' do
      it 'creates event and contract in current tenant context' do
        ActsAsTenant.with_tenant(company_a) do
          event = create(:event, :in_current_tenant)
          
          expect(event.company).to eq(company_a)
          expect(event.contract.company).to eq(company_a)
        end
      end
    end

    describe ':with_validated_context trait' do
      it 'raises error when event company context is misaligned' do
        ActsAsTenant.with_tenant(company_a) do
          expect { create(:event, :with_validated_context, company: company_b) }
            .to raise_error(StandardError)
            .with_message(/EVENT FACTORY CONTEXT MISMATCH - TYM-30 SCENARIO DETECTED/)
        end
      end

      it 'raises error when contract company context is misaligned' do
        contract_b = nil
        ActsAsTenant.with_tenant(company_b) do
          contract_b = create(:contract, company: company_b)
        end

        ActsAsTenant.with_tenant(company_a) do
          expect { create(:event, :with_validated_context, contract: contract_b, company: company_a) }
            .to raise_error(StandardError) do |error|
              expect(error.message).to include("This is the EXACT configuration that caused 2+ hours of debugging hell!")
              expect(error.message).to include("Event will be invisible to API endpoints")
            end
        end
      end

      it 'raises error when event and contract companies are misaligned' do
        contract_b = nil 
        ActsAsTenant.with_tenant(company_b) do
          contract_b = create(:contract, company: company_b)
        end

        ActsAsTenant.with_tenant(company_a) do
          expect { create(:event, :with_validated_context, contract: contract_b, company: company_a) }
            .to raise_error(StandardError)
            .with_message(/Event company_id.*!= Contract company_id/)
        end
      end

      it 'passes all validations when contexts are properly aligned' do
        ActsAsTenant.with_tenant(company_a) do
          contract = create(:contract, company: company_a)
          
          expect(Rails.logger).to receive(:info)
            .with(/Event context validation passed - all contexts aligned/)
          
          expect { create(:event, :with_validated_context, contract: contract, company: company_a) }
            .not_to raise_error
        end
      end
    end

    describe 'TYM-30 specific test event traits' do
      it 'creates test illness event with correct attributes' do
        event = create(:event, :test_illness_event)
        
        expect(event.event_type).to eq('illness')
        expect(event.title).to eq('Test Illness Event')
        expect(event.start_time).to be_within(1.minute).of(Time.current)
        expect(event.end_time).to be_within(1.minute).of(Time.current + 8.hours)
      end

      it 'creates test vacation event with correct attributes' do
        event = create(:event, :test_vacation_event)
        
        expect(event.event_type).to eq('vacation')
        expect(event.title).to eq('Test Vacation Event')
        expect(event.start_time).to be_within(1.minute).of(Time.current + 1.day)
        expect(event.end_time).to be_within(1.minute).of(Time.current + 2.days)
      end
    end
  end

  describe 'TenantAwareFactories helper methods' do
    describe '#create_in_tenant_context' do
      it 'creates factory object in specified company context' do
        contract = create_in_tenant_context(:contract, company: company_a, first_name: 'Test')
        
        expect(contract.company).to eq(company_a)
        expect(contract.first_name).to eq('Test')
      end

      it 'switches tenant context if different from current' do
        ActsAsTenant.with_tenant(company_b) do
          expect(Rails.logger).to receive(:info)
            .with(/Switching context from #{company_b.id} to #{company_a.id}/)
          expect(Rails.logger).to receive(:info)
            .with(/Restored context to #{company_b.id}/)
          
          contract = create_in_tenant_context(:contract, company: company_a)
          expect(contract.company).to eq(company_a)
          
          # Context should be restored
          expect(ActsAsTenant.current_tenant).to eq(company_b)
        end
      end

      it 'raises ArgumentError for invalid company parameter' do
        expect { create_in_tenant_context(:contract, company: 'invalid') }
          .to raise_error(ArgumentError, /Expected Company object/)
      end
    end

    describe '#create_test_scenario_with_jwt' do
      it 'creates multiple factories with aligned JWT token' do
        result = create_test_scenario_with_jwt(
          user, 
          company_a, 
          { 
            contract: { first_name: 'Test' },
            event: { event_type: 'illness' }
          }
        )
        
        expect(result[:objects][:contract]).to be_a(Contract)
        expect(result[:objects][:contract].company).to eq(company_a)
        expect(result[:objects][:event]).to be_a(Event)
        expect(result[:objects][:event].company).to eq(company_a)
        expect(result[:jwt_token]).to be_present
        expect(result[:company]).to eq(company_a)
        expect(result[:user]).to eq(user)
      end

      it 'ensures user has access to target company' do
        new_user = create(:user)
        expect(new_user.companies).not_to include(company_a)
        
        result = create_test_scenario_with_jwt(new_user, company_a, { contract: {} })
        
        expect(new_user.reload.companies).to include(company_a)
      end
    end

    describe '#create_standard_test_company_with_users' do
      it 'creates company with owner, admin, and employee users' do
        result = create_standard_test_company_with_users
        
        expect(result[:company]).to be_a(Company)
        expect(result[:owner]).to be_a(User)
        expect(result[:admin]).to be_a(User)
        expect(result[:employee]).to be_a(User)
        
        # Verify associations
        expect(result[:owner].companies).to include(result[:company])
        expect(result[:admin].companies).to include(result[:company])
        expect(result[:employee].companies).to include(result[:company])
        
        # Verify contracts created in tenant context
        ActsAsTenant.with_tenant(result[:company]) do
          expect(Contract.where(user: result[:owner])).to exist
          expect(Contract.where(user: result[:admin])).to exist
          expect(Contract.where(user: result[:employee])).to exist
        end
      end

      it 'creates unique test data for multiple calls' do
        result1 = create_standard_test_company_with_users
        result2 = create_standard_test_company_with_users
        
        expect(result1[:company]).not_to eq(result2[:company])
        expect(result1[:owner].email).not_to eq(result2[:owner].email)
      end
    end
  end
end