FactoryBot.define do
  factory :event do
    association :contract, :with_user
    association :company
    start_time { Time.current.beginning_of_day + 9.hours }
    end_time { start_time + 8.hours }
    event_type { Event.event_types.keys.sample }
    status { 'pending' }
    description { "Test event description" }

    trait :with_user do
      user { contract.user }
    end

    trait :illness do
      event_type { 'illness' }
    end

    trait :day_care do
      event_type { 'day_care' }
    end

    trait :family_sick do
      event_type { 'family_sick' }
    end

    trait :other do
      event_type { 'other' }
    end

    trait :vacation do
      event_type { 'vacation' }
    end

    trait :travel do
      event_type { 'travel' }
    end

    # Tenant-aware traits to prevent TYM-30 context mismatch issues
    # Events were central to the TYM-30 debugging nightmare
    trait :tenant_aware do
      after(:build) do |event|
        current_tenant = ActsAsTenant.current_tenant
        if current_tenant && event.company_id != current_tenant.id
          Rails.logger.warn "[EVENT_FACTORY] ⚠️  Event context mismatch detected!"
          Rails.logger.warn "[EVENT_FACTORY] - ActsAsTenant.current_tenant: #{current_tenant.id} (#{current_tenant.name})"
          Rails.logger.warn "[EVENT_FACTORY] - Event.company_id: #{event.company_id}"
          Rails.logger.warn "[EVENT_FACTORY] - This could cause API to find 0 events (TYM-30 scenario)"
        end
        
        # Also check contract context alignment
        if event.contract && current_tenant && event.contract.company_id != current_tenant.id
          Rails.logger.warn "[EVENT_FACTORY] ⚠️  Event contract context mismatch!"
          Rails.logger.warn "[EVENT_FACTORY] - Contract.company_id: #{event.contract.company_id}"
          Rails.logger.warn "[EVENT_FACTORY] - Current tenant: #{current_tenant.id}"
        end
      end
    end

    trait :in_current_tenant do
      company { ActsAsTenant.current_tenant || FactoryBot.create(:company) }
      contract { FactoryBot.create(:contract, company: company) }
      
      after(:build) do |event|
        if ActsAsTenant.current_tenant.nil?
          Rails.logger.warn "[EVENT_FACTORY] No current tenant set, created new company and contract"
        end
      end
    end

    # Create event with strict context validation - exactly what would prevent TYM-30
    trait :with_validated_context do
      after(:create) do |event|
        current_tenant = ActsAsTenant.current_tenant
        
        context_errors = []
        
        # Check event company context
        if current_tenant && event.company_id != current_tenant.id
          context_errors << "Event company_id (#{event.company_id}) != ActsAsTenant.current_tenant (#{current_tenant.id})"
        end
        
        # Check contract company context  
        if event.contract && current_tenant && event.contract.company_id != current_tenant.id
          context_errors << "Contract company_id (#{event.contract.company_id}) != ActsAsTenant.current_tenant (#{current_tenant.id})"
        end
        
        # Check event-contract company alignment
        if event.contract && event.company_id != event.contract.company_id
          context_errors << "Event company_id (#{event.company_id}) != Contract company_id (#{event.contract.company_id})"
        end
        
        if context_errors.any?
          error_message = <<~ERROR
            🚨 EVENT FACTORY CONTEXT MISMATCH - TYM-30 SCENARIO DETECTED! 🚨
            
            This is the EXACT configuration that caused 2+ hours of debugging hell!
            
            PROBLEMS DETECTED:
            #{context_errors.map { |error| "  - #{error}" }.join("\n")}
            
            IMPACT:
            - API calls will search in wrong company context
            - Event will be invisible to API endpoints
            - You'll think the feature is broken when it's actually working
            - Hours of debugging phantom problems
            
            EVENT DETAILS:
            - Event ID: #{event.id} (#{event.event_type})
            - Event Company: #{event.company_id}
            - Contract Company: #{event.contract&.company_id}
            - Current Tenant: #{current_tenant&.id}
            
            SOLUTION:
            1. Create event in correct tenant context:
               ActsAsTenant.with_tenant(Company.find(#{current_tenant&.id || 'TARGET_COMPANY_ID'})) do
                 event = create(:event, :with_validated_context)
               end
               
            2. Or use tenant-aware factory helper:
               event = create_in_tenant_context(:event, company: your_company)
          ERROR
          
          raise StandardError, error_message
        end
        
        Rails.logger.info "[EVENT_FACTORY] ✅ Event context validation passed - all contexts aligned"
      end
    end

    # TYM-30 specific test event traits (matching the debug scripts)
    trait :test_illness_event do
      event_type { 'illness' }
      title { 'Test Illness Event' }
      start_time { Time.current }
      end_time { Time.current + 8.hours }
    end

    trait :test_vacation_event do
      event_type { 'vacation' }
      title { 'Test Vacation Event' }
      start_time { Time.current + 1.day }
      end_time { Time.current + 2.days }
    end
  end
end 