FactoryBot.define do
  factory :contract do
    association :company
    first_name { "Test" }
    last_name { "Contractor" }
    sequence(:email) { |n| "contractor#{n}@example.com" }
    contract_type { "employee" }
    active { true }
    status { 0 }
    valid_since { Time.current }
    valid_through { 1.year.from_now }

    trait :with_user do
      association :user
      after(:create) do |contract|
        contract.update_column(:email, contract.user.email)
      end
    end

    trait :skip_invitation do
      after(:build) do |contract|
        def contract.send_invitation; end
      end
    end

    # Tenant-aware traits to prevent TYM-30 context mismatch issues
    trait :tenant_aware do
      after(:build) do |contract|
        # Validate tenant context alignment 
        current_tenant = ActsAsTenant.current_tenant
        if current_tenant && contract.company_id != current_tenant.id
          Rails.logger.warn "[CONTRACT_FACTORY] ⚠️  Potential context mismatch detected!"
          Rails.logger.warn "[CONTRACT_FACTORY] - ActsAsTenant.current_tenant: #{current_tenant.id} (#{current_tenant.name})"
          Rails.logger.warn "[CONTRACT_FACTORY] - Contract.company_id: #{contract.company_id}"
          Rails.logger.warn "[CONTRACT_FACTORY] - This could cause TYM-30 debugging hell scenario"
        end
      end
    end

    trait :in_current_tenant do
      company { ActsAsTenant.current_tenant || FactoryBot.create(:company) }
      
      after(:build) do |contract|
        if ActsAsTenant.current_tenant.nil?
          Rails.logger.warn "[CONTRACT_FACTORY] No current tenant set, created new company for contract"
        end
      end
    end

    # Create contract with explicit company context validation
    trait :with_validated_context do
      after(:create) do |contract|
        current_tenant = ActsAsTenant.current_tenant
        if current_tenant && contract.company_id != current_tenant.id
          error_message = <<~ERROR
            🚨 CONTRACT FACTORY CONTEXT MISMATCH 🚨
            
            Contract created in Company #{contract.company_id} but ActsAsTenant context is Company #{current_tenant.id}
            
            This is the exact TYM-30 scenario that causes debugging hell!
            
            SOLUTION:
            1. Use ActsAsTenant.with_tenant(company) wrapper:
               ActsAsTenant.with_tenant(your_company) do
                 contract = create(:contract, :with_validated_context)
               end
               
            2. Or use tenant-aware factory helper:
               contract = create_in_tenant_context(:contract, company: your_company)
               
            3. Or create without validation:
               contract = create(:contract) # without :with_validated_context trait
          ERROR
          
          raise StandardError, error_message
        end
      end
    end
  end
end 