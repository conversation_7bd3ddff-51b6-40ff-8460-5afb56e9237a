# frozen_string_literal: true

# ABOUTME: Comprehensive RSpec tests for TenantContextValidator to prevent TYM-30 scenarios
# ABOUTME: by validating context alignment between ActsAsTenant and JWT tokens.

require 'rails_helper'

RSpec.describe TenantContextValidator, type: :module do
  include described_class
  
  let(:company_a) { create(:company, name: 'Company A') }
  let(:company_b) { create(:company, name: 'Company B') }
  let(:user) { create(:user) }
  let(:role) { create(:role, name: 'employee') }

  before do
    create(:company_user_role, user: user, company: company_a, role: role)
    create(:company_user_role, user: user, company: company_b, role: role)
  end

  describe '#ensure_tenant_jwt_context_match' do
    context 'when contexts are aligned' do
      it 'passes validation without error' do
        ActsAsTenant.with_tenant(company_a) do
          token = generate_jwt_for_company(user, company_a)
          
          expect { ensure_tenant_jwt_context_match(token) }.not_to raise_error
        end
      end

      it 'returns true for aligned contexts' do
        ActsAsTenant.with_tenant(company_a) do
          token = generate_jwt_for_company(user, company_a)
          
          result = ensure_tenant_jwt_context_match(token)
          expect(result).to be true
        end
      end
    end

    context 'when contexts are misaligned (TYM-30 scenario)' do
      it 'raises ContextMismatchError with detailed explanation' do
        ActsAsTenant.with_tenant(company_a) do
          token = generate_jwt_for_company(user, company_b)
          
          expect { ensure_tenant_jwt_context_match(token) }
            .to raise_error(TenantContextValidator::ContextMismatchError)
            .with_message(/TENANT CONTEXT MISMATCH DETECTED/)
        end
      end

      it 'includes specific company details in error message' do
        ActsAsTenant.with_tenant(company_a) do
          token = generate_jwt_for_company(user, company_b)
          
          expect { ensure_tenant_jwt_context_match(token) }
            .to raise_error(TenantContextValidator::ContextMismatchError) do |error|
              expect(error.message).to include("JWT Token expects Company ID: #{company_b.id}")
              expect(error.message).to include("ActsAsTenant context is: #{company_a.id}")
              expect(error.message).to include("TYM-30 debugging hell")
            end
        end
      end
    end

    context 'with expected_company parameter' do
      it 'validates against expected company instead of current tenant' do
        ActsAsTenant.with_tenant(company_a) do
          token = generate_jwt_for_company(user, company_b)
          
          expect { ensure_tenant_jwt_context_match(token, expected_company: company_b) }
            .not_to raise_error
        end
      end
    end

    context 'with nil or empty token' do
      it 'returns true for nil token' do
        result = ensure_tenant_jwt_context_match(nil)
        expect(result).to be true
      end

      it 'returns true for empty token' do
        result = ensure_tenant_jwt_context_match('')
        expect(result).to be true
      end
    end
  end

  describe '#create_test_data_with_matching_jwt_context' do
    it 'sets tenant context to specified company' do
      original_tenant = ActsAsTenant.current_tenant
      
      token = create_test_data_with_matching_jwt_context(user, company_a)
      
      expect(ActsAsTenant.current_tenant).to eq(company_a)
    end

    it 'returns JWT token aligned with company context' do
      token = create_test_data_with_matching_jwt_context(user, company_a)
      
      expect { ensure_tenant_jwt_context_match(token, expected_company: company_a) }
        .not_to raise_error
    end

    it 'logs context alignment success' do
      expect(Rails.logger).to receive(:info)
        .with(/Test data context aligned - Company: #{company_a.id}/)
      
      create_test_data_with_matching_jwt_context(user, company_a)
    end
  end

  describe '#debug_current_context' do
    context 'with aligned contexts' do
      it 'returns context information with aligned flag true' do
        ActsAsTenant.with_tenant(company_a) do
          token = generate_jwt_for_company(user, company_a)
          
          result = debug_current_context(token)
          
          expect(result).to include(
            acts_as_tenant_current: company_a.id,
            acts_as_tenant_name: company_a.name,
            jwt_company_id: company_a.id,
            jwt_company_name: company_a.name,
            context_aligned: true
          )
        end
      end
    end

    context 'with misaligned contexts' do
      it 'returns context information with aligned flag false' do
        ActsAsTenant.with_tenant(company_a) do
          token = generate_jwt_for_company(user, company_b)
          
          result = debug_current_context(token)
          
          expect(result).to include(
            acts_as_tenant_current: company_a.id,
            jwt_company_id: company_b.id,
            context_aligned: false
          )
        end
      end

      it 'logs potential mismatch warning' do
        expect(Rails.logger).to receive(:warn)
          .with(/POTENTIAL MISMATCH DETECTED/)
        expect(Rails.logger).to receive(:warn)
          .with(/This may cause phantom debugging problems similar to TYM-30/)
        
        ActsAsTenant.with_tenant(company_a) do
          token = generate_jwt_for_company(user, company_b)
          debug_current_context(token)
        end
      end
    end

    context 'without token' do
      it 'returns basic context information' do
        ActsAsTenant.with_tenant(company_a) do
          result = debug_current_context
          
          expect(result).to include(
            acts_as_tenant_current: company_a.id,
            acts_as_tenant_name: company_a.name,
            jwt_company_id: nil,
            context_aligned: false
          )
        end
      end
    end
  end

  private

  def generate_jwt_for_company(user, company)
    if defined?(JwtService)
      JwtService.encode(user_id: user.id, company_id: company.id)
    else
      payload = {
        user_id: user.id,
        company_id: company.id,
        iat: Time.current.to_i,
        exp: 24.hours.from_now.to_i
      }
      JWT.encode(payload, Rails.application.secret_key_base, 'HS256')
    end
  end
end